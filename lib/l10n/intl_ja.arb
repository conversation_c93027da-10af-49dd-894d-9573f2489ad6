{"@@locale": "ja", "appName": "トピンホーム", "homeTitle": "マイ製品", "homeAddHint": "製品を追加して、より良い操作体験をお楽しみください", "addDevice": "デバイスを追加", "noDevice": "デバイスなし", "addDeviceHint": "製品を追加して、より良い操作体験をお楽しみください", "foundDevice": "デバイスを発見", "devices": "デバイス", "loginClick": "タップしてログイン", "about": "概要", "quickStart": "クイックスタート", "feedback": "フィードバック", "customBackground": "背景のカスタマイズ", "logout": "ログアウト", "registerTitle": "登録", "phone": "電話番号", "phoneHint": "電話番号を入力してください", "verifyCode": "認証コード", "getSmsVerifyCode": "SMSコードを取得", "agreement": "利用規約とプライバシーポリシーに同意します", "agreementLink": "《利用規約》", "agree": "同意", "privacyPolicy": "《プライバシーポリシー》", "register": "登録", "login": "ログイン", "loginTitle": "ログイン", "loginPhoneHint": "電話番号を入力してください", "loginVerifyCode": "認証コード", "loginGetSmsVerifyCode": "SMSコードを取得", "loginPassword": "パスワードでログイン", "loginForgetPassword": "パスワードをお忘れですか", "loginFail": "ログイン失敗", "passwordError": "パスワードエラー", "phoneNotRegistered": "登録されていない電話番号", "noAccount": "アカウントをお持ちでないですか？", "registerNow": "今すぐ登録", "tips": "ヒント", "graphicCodeHint": "画像認証コードを入力してください", "agreeToTermsHint": "利用規約とプライバシーポリシーに同意してください", "verificationCodeHint": "認証コードを入力してください", "usernameHint": "ユーザー名を入力してください", "passwordHint": "パスワードを入力してください", "confirmPasswordHint": "パスワードを再入力してください", "passwordMismatch": "パスワードが一致しません", "registerSuccess": "登録成功", "registerSuccessHint": "電話番号とパスワードでログインしてください", "registerFailed": "登録失敗", "getVerificationCode": "認証コードを取得", "verificationCodeSent": "認証コードが送信されました", "next": "次へ", "secondResend": "秒後に再送信", "settingAccount": "アカウント設定", "registerComplete": "登録完了", "username": "ユーザー名", "password": "パスワード", "confirmPassword": "パスワード確認", "inputError": "入力エラー", "inputCannotBeEmpty": "入力は空にできません", "invalidCaptcha": "認証コードエラー", "forgetPassword": "パスワードをお忘れの方", "forgetPasswordTitle": "パスワードを忘れた", "forgetPasswordPhoneTitle": "電話番号を入力してください", "forgetPasswordPhoneHint": "登録済みの電話番号で認証コードを受け取り、次のステップに進みます", "forgetPasswordVerifyCode": "認証コード", "forgetPasswordGetSmsVerifyCode": "SMSコードを取得", "forgetPasswordNext": "次へ", "forgetPasswordNewPassword": "新しいパスワード", "forgetPasswordNewPasswordHint": "新しいパスワードを入力してください", "forgetPasswordConfirmPassword": "パスワード確認", "forgetPasswordConfirmPasswordHint": "新しいパスワードを再入力してください", "forgetPasswordReset": "パスワードをリセット", "forgetPasswordSuccess": "パスワードのリセット成功", "forgetPasswordSuccessHint": "新しいパスワードでログインしてください", "forgetPasswordSuccessBack": "ログインに戻る", "forgetPasswordSuccessBackHome": "ホームに戻る", "forgetPasswordSuccessBackLogin": "ログインに戻る", "forgetPasswordSuccessBackRegister": "登録に戻る", "forgetPasswordSuccessBackForgetPassword": "パスワード忘れに戻る", "aboutTitle": "概要", "aboutVersion": "バージョン", "aboutVersionHint": "1.0.0", "aboutUpdate": "更新を確認", "aboutUpdateHint": "最新のバージョンです", "aboutUpdateSuccess": "更新成功", "feedbackTitle": "フィードバック", "feedbackHint": "ご意見をいただければ、製品の改善に努めます", "feedbackContact": "連絡先", "feedbackContactHint": "連絡先を入力してください", "feedbackContent": "フィードバック内容", "feedbackContentHint": "フィードバック内容を入力してください", "feedbackImage": "画像をアップロード", "feedbackImageHint": "最大5枚までアップロード可能", "feedbackDevice": "デバイスモデル", "feedbackDeviceHint": "デバイスモデルを選択してください", "feedbackSubmit": "送信", "feedbackSuccess": "送信完了", "feedbackSuccessHint": "フィードバックありがとうございます。できるだけ早く対応いたします", "feedbackType": "フィードバックタイプ", "feedbackTypeRequired": "フィードバックタイプを選択してください", "feedbackError": "送信失敗、後でもう一度お試しください", "error": "エラー", "logoutTitle": "ログアウト", "logoutHint": "ログアウトしますか？", "cancel": "キャンセル", "confirm": "確認", "deviceNotFoundTitle": "デバイスが見つかりませんか？", "deviceNotFoundHint": "1. デバイスの電源が入っているか確認してください\n2. デバイスとスマートフォンのBluetooth接続を確認してください\n3. 上記の手順を完了したら、更新ボタンをタップして再検索してください\n4. 一部のスマートフォンではBluetooth BLE接続に位置情報の有効化が必要な場合があります。位置情報を有効にしてから再度更新してください", "operateDescription": "操作説明", "blueHeadAmp": "Bluetoothヘッドフォンアンプ", "decoderAmp": "デコーダーアンプ", "headset": "ヘッドフォン", "bluetoothHeadphoneAmplifier": "BluetoothとヘッドフォンアンプリファイアR", "player": "プレーヤー", "inputLinker": "Linker制御IPを手動入力", "connect": "接続済み", "disconnected": "切断済み", "connecting": "接続中", "disconnecting": "切断中", "disconnect": "未接続", "personalCenter": "マイページ", "editPersonalInfo": "個人情報を編集", "accountSecurity": "アカウントセキュリティ", "avatar": "アバター", "gender": "性別", "secret": "非公開", "male": "男性", "female": "女性", "birthday": "誕生日", "signature": "自己紹介", "region": "地域", "nickname": "ニックネーム", "save": "保存", "success": "成功", "updateSuccess": "更新成功", "userNotFound": "ユーザーが見つかりません", "loginPasswordModify": "ログインパスワードの変更", "accountAndBinding": "アカウントと連携設定", "modifyPhone": "電話番号の変更", "cancelAccount": "アカウント削除", "modifyPassword": "パスワード変更", "oldPassword": "現在のパスワード", "newPassword": "新しいパスワード", "confirmNewPassword": "新しいパスワード（確認）", "modifyPasswordSuccess": "パスワード変更が完了しました", "informationIncomplete": "入力情報が不完全です", "oldPasswordError": "現在のパスワードが間違っています", "newPasswordAndConfirmPasswordNotConsistent": "新しいパスワードと確認用パスワードが一致しません", "oldPasswordAndNewPasswordCannotBeTheSame": "現在のパスワードと新しいパスワードは同じにできません", "newPasswordCannotBeTheSameAsTheOldPassword": "新しいパスワードは現在のパスワードと同じにできません", "emailBinding": "メール連携", "notBound": "未連携", "bound": "連携済み", "nowEmailVerification": "メール認証", "emailVerificationPrompt": "連携するメールアドレスを入力してください。後でメール変更やパスワード再設定に使用できます", "email": "メールアドレス", "sendVerificationCode": "認証コードを送信", "smsVerificationCode": "SMS認証コード", "verificationCodeHasBeenSent": "認証コードを送信しました", "accountBindingSuccess": "アカウント連携完了", "emailEmpty": "メールアドレスを入力してください", "invalidEmail": "無効なメールアドレス形式です", "emailBindSuccess": "メール連携完了", "unbindEmail": "メール連携解除", "unbindEmailConfirm": "メールの連携を解除しますか？", "emailUnbindSuccess": "メール連携解除完了", "boundEmail": "連携済みメール", "unbind": "連携解除", "bind": "連携する", "getEmailVerificationCode": "メール認証コードを取得", "bindNewPhone": "新しい電話番号を連携", "bindNewPhonePrompt": "新しい電話番号を入力し、認証コードを取得して確認してください", "bindNewPhoneSuccess": "新しい電話番号の連携完了", "bindNewPhoneFailure": "新しい電話番号の連携に失敗しました", "bindNewPhoneFailurePrompt": "新しい電話番号の連携に失敗しました。後でもう一度お試しください", "will": "は", "accountWillBeCancelled": "連携されたアカウントを削除", "cancellationInstructions": "注意：アカウントの削除はご本人のみが行えます。削除後は復元できませんので、よく検討してください。\nアカウント削除後は、このアカウントを使用できなくなり、アカウントに関連するすべての情報も復元できません：\n\n1. このアカウントの個人情報（プロフィール画像、ニックネーム、連携製品など）\n2. このアカウントの特典（MQAメンバーシップ、フォーラムポイントなど）\n\n3. このアカウントのコンテンツ（プレイリストなど）\n\n4. アカウント削除によって生じるその他の結果", "cancellation": "削除", "confirmCancellation": "アカウントを削除しますか？", "cancellationSuccess": "削除成功", "cancellationFailure": "削除失敗", "exitApp": "アプリを終了", "exitAppPrompt": "アプリを終了しますか？", "phoneUpdateSuccess": "電話番号の更新が完了しました", "phoneEmpty": "電話番号を入力してください", "verificationCodeEmpty": "認証コードを入力してください", "samePhoneNumber": "新しい電話番号が現在の番号と同じです", "verifyOldPhone": "現在の電話番号を確認", "setNewPhone": "新しい電話番号を設定", "oldPhone": "現在の電話番号", "newPhone": "新しい電話番号", "passwordTooShort": "パスワードは6文字以上必要です", "passwordEmpty": "パスワードを入力してください", "passwordUpdateSuccess": "パスワード更新が完了しました", "passwordUpdateSuccessRelogin": "パスワードの更新が完了しました。再度ログインしてください", "scanning": "スキャン中", "scanningDevicesHint": "近くのBluetoothデバイスをスキャンしています...", "startScanningHint": "スキャン開始", "manualInputIP": "IPを手動入力", "manualInputIPHint": "Linker制御IPを入力してください", "bluetoothAndDac": "BluetoothとDAC", "streamer": "ストリーマー", "pleaseInputIP": "IPを入力してください", "deviceNotFoundHint1": "1. デバイスの電源が入っていることを確認してください。", "deviceNotFoundHint2": "2. デバイスとスマートフォンのBluetooth接続が正常かを確認してください。", "deviceNotFoundHint3": "3. 上記の手順を完了したら、更新ボタンをタップして再検索してください。", "deviceNotFoundHint4": "4. 一部のスマートフォンではBluetooth BLE接続に位置情報の有効化が必要な場合があります。位置情報を有効にしてから再度更新してください。", "invalidQRCode": "無効なQRコード", "scanQRCode": "QRコードをスキャン", "scanQRCodeHint": "デバイスのQRコードをスキャンしてください", "scanQRCodeBottomHint": "QRコードを枠内に合わせると自動的にスキャンされます", "noDevicesFound": "デバイスが見つかりません", "discoveredDevices": "接続可能な製品", "discoveredDevicesHint": "複数の接続可能な製品が見つかりました", "tapToConnect": "タップして接続", "availableDevices": "利用可能なデバイス", "languageCode": "ja", "deviceNotFound": "デバイスが見つかりません", "connected": "接続済み", "battery": "バッテリー", "tryAgain": "もう一度試す", "volumeControl": "音量調整", "unlock": "ロック解除", "lock": "ロック", "audioSettings": "オーディオ設定", "displayView": "表示画面", "low": "低", "middle": "中", "high": "高", "equalizer": "イコライザー", "eqPreset": "EQプリセット", "digitalFilter": "デジタルフィルター", "channelBalance": "チャンネルバランス", "dsdMode": "DSDモード", "systemSettings": "システム設定", "displayBrightness": "画面の明るさ", "displayTimeout": "画面のタイムアウト", "autoPowerOff": "自動電源オフ", "disabled": "無効", "ledIndicator": "LEDインジケーター", "connectToAccessSettings": "設定にアクセスするには接続してください", "connectionInfo": "接続情報", "codec": "コーデック", "deviceInfo": "デバイス情報", "firmwareVersion": "ファームウェアバージョン", "serialNumber": "シリアル番号", "totalPlayTime": "総再生時間", "deleteDevice": "デバイスを削除", "deleteDeviceConfirm": "このデバイスを削除しますか？", "delete": "削除", "preamplifier": "プリアンプ", "decodeModeDac": "DAC", "displayVu": "VUメーター", "displayFft": "スペクトラム", "displayNormal": "通常", "inputUsb": "USB", "inputOptical": "光デジタル", "inputCoaxial": "同軸デジタル", "inputBluetooth": "Bluetooth", "languageZh": "中国語", "languageEn": "英語", "headphoneGainHigh": "高ゲイン", "headphoneGainLow": "低ゲイン", "multiFunctionKeyInputSelect": "入力選択", "multiFunctionKeyLineOutSelect": "ライン出力選択", "multiFunctionKeyHeadphoneOutSelect": "ヘッドフォン出力選択", "multiFunctionKeyHomeSelect": "ホーム選択", "multiFunctionKeyBrightnessSelect": "明るさ選択", "multiFunctionKeySleep": "スリープ", "multiFunctionKeyPcmFilterSelect": "PCMフィルター選択", "multiFunctionKeyMute": "ミュート", "multiFunctionKeyPeqSelect": "PEQ選択", "outputClose": "閉じる", "outputSingleEnded": "RCA", "outputBalanced": "XLR", "outputSingleEndedAndBalanced": "RCA+XLR", "powerTriggerSignal": "シグナル", "powerTriggerVoltage": "12V", "powerTriggerClose": "オフ", "screenBrightnessHigh": "高", "screenBrightnessMedium": "中", "screenBrightnessLow": "低", "screenBrightnessAuto": "自動", "themeAurora": "オーロラ", "themeOrange": "オレンジ", "themePeru": "ペルー", "themeGreen": "グリーン", "themeKhaki": "カーキ", "themeRose": "ローズ", "themeBlue": "ブルー", "themePurple": "パープル", "themeWhite": "ホワイト", "darkMode": "ダークモード", "lightMode": "ライトモード", "darkModeDescription": "現在ダークテーマを使用中", "lightModeDescription": "現在ライトテーマを使用中", "usbTypeUac2": "UAC 2.0", "usbTypeUac1": "UAC 1.0", "deviceTypeBluetooth": "Bluetooth", "deviceTypeDac": "DAC", "deviceTypeHeadphone": "ヘッドフォンアンプ", "deviceTypePlayer": "プレーヤー", "name": "名前", "volume": "音量", "headphoneOutput": "ヘッドフォン出力", "headphoneGain": "ヘッドフォンゲイン", "inputSelect": "入力選択", "outputSelect": "出力選択", "advanced": "詳細", "advancedSettings": "詳細設定", "editName": "名前を編集", "enterNewName": "新しい名前を入力してください", "setting": "設定", "peq": "イコライザー", "guide": "操作ガイド", "theme": "テーマ", "powerTrigger": "電源トリガー", "audioBalance": "オーディオバランス", "filter": "フィルター", "decodeMode": "デコードモード", "audioMonitoring": "オーディオBluetooth", "bluetoothAptx": "Bluetooth aptX", "relay": "リモコン", "multiFunctionKey": "多機能キーのカスタマイズ", "usbMode": "USB", "screenBrightness": "画面の明るさ", "language": "言語", "resetSettings": "詳細設定をリセット", "restoreFactorySettings": "工場出荷時設定に戻す", "channel": "チャンネル", "resetSettingsConfirmation": "詳細設定をリセットしますか？", "restoreFactorySettingsConfirmation": "工場出荷時設定に戻しますか？", "import": "インポート", "export": "エクスポート", "target": "ターゲット", "sourceFR": "ソースFR", "eachFilter": "各フィルター", "combinedFilter": "結合フィルター", "filteredFR": "フィルター後の周波数応答", "raw": "生信号", "compensated": "補正", "preAmplification": "プリアンプ", "peakingFilter": "ピーキングフィルター", "lowPassFilter": "ローパスフィルター", "highPassFilter": "ハイパスフィルター", "lowShelfFilter": "ローシェルフフィルター", "highShelfFilter": "ハイシェルフフィルター", "centerFrequency": "中心周波数", "connerFrequency": "コーナー周波数", "type": "種類", "frequency": "周波数", "q": "Q値", "gain": "G値", "addFilter": "フィルターを追加", "mode": "モード", "import_target": "ターゲットカーブのインポート", "import_sourceFR": "ソース周波数応答カーブのインポート", "selectDataFile": "データファイルを選択", "problemType": "問題の種類", "selectDeviceType": "デバイスタイプを選択", "device": "デバイス", "addPicture": "画像を追加", "contact": "連絡先", "feedbackTypeFeatureSuggestion": "機能の提案", "feedbackTypeBugReport": "バグ報告", "feedbackTypeUIImprovement": "UI改善", "feedbackTypeOther": "その他", "checkUpdate": "更新を確認", "checking": "確認中...", "alreadyLatestVersion": "最新バージョンです", "checkUpdateFailed": "更新の確認に失敗しました", "foundNewVersion": "新しいバージョンが見つかりました", "currentVersion": "現在のバージョン", "newVersion": "新しいバージョン", "updateContent": "更新内容", "later": "後で", "updateNow": "今すぐ更新", "downloading": "ダウンロード中...", "downloadCompleted": "ダウンロード完了", "downloadFailed": "ダウンロード失敗", "storagePermissionDenied": "ストレージアクセス権限が拒否されました", "cannotGetDownloadPath": "ダウンロードパスを取得できません", "installPermissionDenied": "インストール権限が拒否されました", "failedToLoadContent": "コンテンツの読み込みに失敗しました", "lastUpdated": "最終更新", "termsAndConditions": "利用規約", "agreementDialogContent": "アプリを使用する前に、利用規約とプライバシーポリシーに同意してください", "userAgreement": "利用規約", "privacyPolicyText": "プライバシーポリシー", "disagree": "同意しない", "exportSuccess": "エクスポートに成功しました", "fileSavedTo": "ファイルの保存先：", "fileContentPreview": "ファイル内容のプレビュー：", "ok": "OK", "openFileLocation": "ファイルの場所を開く", "sharedFilterFile": "共有フィルターファイル", "sharedFile": "共有ファイル", "cannotOpenFileOrItsDirectory": "ファイルまたはそのディレクトリを開けません", "errorOpeningFileLocation": "ファイル場所を開く際にエラーが発生しました：", "errorSharingFile": "ファイルの共有中にエラーが発生しました", "cannotParseSelectedFile": "選択されたファイルを解析できません。有効なCSV形式で、周波数と応答データが含まれていることを確認してください。", "cannotExtractValidFrequencyAndResponseDataFromCSV": "CSVから有効な周波数と応答データを抽出できません。", "importSuccess": "インポートに成功しました", "successfullyImportedFile": "ファイルのインポートに成功しました", "errorImportingFile": "ファイルのインポート中にエラーが発生しました", "noDataToExport": "エクスポートするデータがありません。", "fileSavedToAppFolder": "ファイルはアプリフォルダに保存されました：", "errorSavingFile": "ファイル保存中にエラーが発生しました：", "saveMergedFilterFile": "結合されたフィルターファイルを保存", "startUsing": "利用を開始", "nextPage": "次のページ", "background": "背景", "confirmDelete": "削除確認", "confirmDeleteHint": "この背景画像を削除してもよろしいですか？", "opacity": "透明度", "blur": "ぼかし", "selectFromAlbum": "アルバムから選択", "takePhoto": "写真を撮る", "addBackground": "背景を追加", "deleteBackgroundFailed": "背景の削除に失敗しました", "saveBackgroundFailed": "背景の保存に失敗しました", "errorExportingFile": "ファイルのエクスポート中にエラーが発生しました", "skip": "スキップ", "version": "バージョン", "copyright": " 2025 Topping ", "newFirmwareAvailable": "新しいファームウェアが利用可能です", "tip": "ヒント", "firmwareUpgrade": "ファームウェアアップグレード", "firmwareUpgrading": "ファームウェアをアップグレード中", "firmwareUpgradeSuccess": "ファームウェアのアップグレードに成功しました", "firmwareUpgradeFailed": "ファームウェアのアップグレードに失敗しました", "firmwareUpgradeConfirm": "ファームウェアをアップグレードしますか？", "firmwareUpgradeWarning": "アップグレード中はデバイスを切断しないでください", "firmwareSize": "ファームウェアサイズ", "firmwareDescription": "説明", "firmwareForceUpdate": "強制アップデート", "firmwareDownloading": "ファームウェアをダウンロード中", "firmwareInstalling": "ファームウェアをインストール中", "settingResetFail": "リセット失敗", "@settingResetFail": {}, "firmwareUpdateTitle": "ファームウェアアップデート", "@firmwareUpdateTitle": {"description": "Title for the firmware update page"}, "firmwareUpdateNewFirmwareFound": "新しいファームウェアが見つかりました", "@firmwareUpdateNewFirmwareFound": {}, "firmwareUpdateFirmwareName": "ファームウェア名", "@firmwareUpdateFirmwareName": {}, "firmwareUpdateVersion": "バージョン", "@firmwareUpdateVersion": {}, "firmwareUpdateDeviceModel": "デバイスモデル", "@firmwareUpdateDeviceModel": {}, "firmwareUpdateFileSize": "ファイルサイズ", "@firmwareUpdateFileSize": {}, "firmwareUpdateDescription": "更新ノート", "@firmwareUpdateDescription": {}, "firmwareUpdateMandatoryUpdateNote": "※このバージョンは必須アップデートです", "@firmwareUpdateMandatoryUpdateNote": {}, "firmwareUpdateStatus": "アップデート状況", "@firmwareUpdateStatus": {}, "firmwareUpdateDownloading": "ダウンロード中", "@firmwareUpdateDownloading": {}, "firmwareUpdateUpgrading": "アップグレード中", "@firmwareUpdateUpgrading": {}, "firmwareUpdateDoNotDisconnect": "接続を切断したり、デバイスの電源を切ったりしないでください", "@firmwareUpdateDoNotDisconnect": {}, "firmwareUpdateReadyToUpdate": "アップデート準備完了", "@firmwareUpdateReadyToUpdate": {}, "firmwareUpdateCancel": "アップデートをキャンセル", "@firmwareUpdateCancel": {}, "firmwareUpdateStart": "アップデートを開始", "@firmwareUpdateStart": {}, "firmwareUpdateLatestVersion": "既に最新バージョンです", "@firmwareUpdateLatestVersion": {}, "firmwareUpdateNoNeed": "ファームウェアアップデートは不要です", "@firmwareUpdateNoNeed": {}, "firmwareUpdateBack": "戻る", "@firmwareUpdateBack": {}, "firmwareUpdateSuccessTitle": "アップデート成功", "@firmwareUpdateSuccessTitle": {}, "firmwareUpdateSuccessMessage": "デバイスのファームウェアが最新バージョンに更新されました", "@firmwareUpdateSuccessMessage": {}, "firmwareUpdateSuccessRebootMessage": "アップグレード成功、デバイスが再起動しています...", "@firmwareUpdateSuccessRebootMessage": {"description": "Message shown when firmware update succeeds and the device disconnects (likely rebooting)"}, "firmwareUpdateDownloadingTitle": "ダウンロード中", "@firmwareUpdateDownloadingTitle": {}, "firmwareUpdateDownloadingMessage": "ファームウェアファイルをダウンロード中...", "@firmwareUpdateDownloadingMessage": {}, "firmwareUpdateErrorTitle": "エラー", "@firmwareUpdateErrorTitle": {}, "firmwareUpdateDownloadFailed": "ファームウェアのダウンロードに失敗しました", "@firmwareUpdateDownloadFailed": {}, "firmwareUpdateUpgradingTitle": "アップグレード中", "@firmwareUpdateUpgradingTitle": {}, "firmwareUpdateUpgradingMessage": "ファームウェアのアップグレードを開始しています...", "@firmwareUpdateUpgradingMessage": {}, "firmwareUpdateSetDeviceFailed": "デバイスの設定に失敗しました", "@firmwareUpdateSetDeviceFailed": {}, "firmwareUpdateErrorDuringUpdate": "アップグレード中にエラーが発生しました: {error}", "@firmwareUpdateErrorDuringUpdate": {"placeholders": {"error": {"type": "String", "example": "Connection failed"}}}, "firmwareUpdateCancelledTitle": "キャンセル済み", "@firmwareUpdateCancelledTitle": {}, "firmwareUpdateCancelledMessage": "ファームウェアのアップグレードがキャンセルされました", "@firmwareUpdateCancelledMessage": {}, "commonConfirm": "確認", "@commonConfirm": {}, "checkFirmwareUpdate": "ファームウェア更新を確認", "@checkFirmwareUpdate": {"description": "Text for checking for firmware updates"}, "deviceRebootTitle": "デバイスの再起動", "@deviceRebootTitle": {"description": "Title for device reboot notification"}, "deviceRebootMessage": "デバイスが再起動しています、しばらくお待ちください...", "@deviceRebootMessage": {"description": "Message content for device reboot notification"}, "infoTitle": "情報", "@infoTitle": {"description": "Title for information dialog"}, "errorOpeningFile": "ファイルを開く際のエラー: {error}", "@errorOpeningFile": {"placeholders": {"error": {"type": "String", "example": "File not found"}}}, "fileExported": "ファイルがエクスポートされました", "@fileExported": {"description": "Message shown when a file has been exported"}, "shareFile": "ファイルを共有", "@shareFile": {"description": "Button text for sharing a file"}, "peqSettings": "PEQ設定", "@peqSettings": {"description": "PEQ settings page title"}, "importExport": "インポートとエクスポート", "@importExport": {"description": "Import and export section header"}, "configManagement": "設定管理", "@configManagement": {"description": "Configuration management section header"}, "addConfig": "設定追加", "@addConfig": {"description": "Add configuration dialog title"}, "configName": "設定名", "@configName": {"description": "Configuration name input label"}, "description": "説明（オプション）", "@description": {"description": "Description input label"}, "configNameHint": "例：ベースブースト、クリアボーカルなど", "@configNameHint": {"description": "Configuration name hint text"}, "configDescriptionHint": "この設定の目的を簡単に説明してください", "@configDescriptionHint": {"description": "Configuration description hint text"}, "@cancel": {"description": "Cancel button text"}, "add": "追加", "@add": {"description": "Add button text"}, "@save": {"description": "Save button text"}, "@delete": {"description": "Delete button text"}, "addNewConfig": "新しい設定を追加", "@addNewConfig": {"description": "Add new configuration tooltip"}, "importTarget": "ターゲットをインポート", "@importTarget": {"description": "Import target button text"}, "importSourceFR": "ソースFRをインポート", "@importSourceFR": {"description": "Import source FR button text"}, "exportCombinedFilter": "結合フィルターをエクスポート", "@exportCombinedFilter": {"description": "Export combined filter button text"}, "targetFR": "ターゲットFR", "@targetFR": {"description": "Target frequency response label"}, "@sourceFR": {"description": "Source frequency response label"}, "editConfig": "設定編集", "@editConfig": {"description": "Edit configuration dialog title/tooltip"}, "deleteConfig": "設定削除", "@deleteConfig": {"description": "Delete configuration dialog title/tooltip"}, "deleteConfigConfirmation": "設定\"{name}\"を削除してもよろしいですか？この操作は元に戻せません。", "@deleteConfigConfirmation": {"description": "Delete configuration confirmation message", "placeholders": {"name": {"type": "Object"}}}, "deleteTargetFile": "ターゲットファイルを削除", "@deleteTargetFile": {"description": "Delete target file dialog title"}, "deleteSourceFRFile": "ソースFRファイルを削除", "@deleteSourceFRFile": {"description": "Delete source FR file dialog title"}, "deleteFileConfirmation": "ファイル\"{name}\"を削除してもよろしいですか？この操作は元に戻せません。", "@deleteFileConfirmation": {"description": "Delete file confirmation message", "placeholders": {"name": {"type": "Object"}}}, "noConfigsMessage": "まだ設定がありません。\"+\"をクリックして新しい設定を作成してください。", "@noConfigsMessage": {"description": "Message shown when there are no configurations"}, "devicePowerOff": "デバイスの電源が切れています", "@devicePowerOff": {"description": "Device is powered off"}, "devicePowerOffHint": "デバイスを操作するには電源を入れてください", "@devicePowerOffHint": {"description": "Turn on the power to control the device"}, "powerOn": "電源オン", "@powerOn": {"description": "Power on button text"}, "inputOptical1": "光デジタル1", "@inputOptical1": {"description": "Optical 1 input"}, "inputOptical2": "光デジタル2", "@inputOptical2": {"description": "Optical 2 input"}, "inputCoaxial1": "同軸デジタル1", "@inputCoaxial1": {"description": "Coaxial 1 input"}, "inputCoaxial2": "同軸デジタル2", "@inputCoaxial2": {"description": "Coaxial 2 input"}, "inputAes": "AES", "@inputAes": {"description": "AES input"}, "inputIis": "IIS", "@inputIis": {"description": "IIS input"}, "outputDac": "DAC", "@outputDac": {"description": "DAC output"}, "outputPreamp": "プリアンプ", "@outputPreamp": {"description": "Preamp output"}, "outputAll": "全て", "@outputAll": {"description": "All outputs"}, "auto": "自動", "@auto": {"description": "Auto mode"}, "enabled": "有効", "@enabled": {"description": "Enabled state"}, "@disabled": {"description": "Disabled state"}, "standard": "標準", "@standard": {"description": "Standard mode"}, "inverted": "反転", "@inverted": {"description": "Inverted mode"}, "swapped": "交換", "@swapped": {"description": "Swapped mode"}, "displaySignal": "信号", "@displaySignal": {"description": "Signal display"}, "display12V": "12V", "@display12V": {"description": "12V display"}, "displayOff": "オフ", "@displayOff": {"description": "Display off"}, "@multiFunctionKeyPeqSelect": {"description": "PEQ select function key"}, "usbTypeC": "Type-C", "@usbTypeC": {"description": "USB Type-C"}, "usbTypeB": "Type-B", "@usbTypeB": {"description": "USB Type-B"}, "powerTriggerOff": "オフ", "@powerTriggerOff": {"description": "Power trigger off"}, "powerTrigger12V": "12V", "@powerTrigger12V": {"description": "Power trigger 12V"}, "multiFunctionKeyOutputSelect": "出力選択", "@multiFunctionKeyOutputSelect": {"description": "Output select function key"}, "multiFunctionKeyScreenOff": "画面オフ", "@multiFunctionKeyScreenOff": {"description": "Screen off function key"}, "usbSelect": "USB選択", "usbDsdPassthrough": "USB DSDパススルー", "iisPhase": "IISフェーズ", "iisDsdChannel": "IIS DSDチャンネル", "classicVu0dBAmplitude": "クラシックVU 0dB振幅", "vuMeterDisplayMode": "VUメーター表示モード", "aurora": "オーロラ", "classic": "クラシック", "simple": "シンプル", "normal": "通常", "fullOpen": "全開", "halfOpen": "半開", "close": "閉じる", "usbAudioClass": "USBオーディオクラス", "usbPortSelect": "USBポート選択", "dsdMute": "DSD MUTE", "bluetoothFunction": "Bluetooth機能", "highLevelActive": "ハイレベルアクティブ", "lowLevelActive": "ローレベルアクティブ", "pcmFilter": "PCMフィルター", "volumeStepSetting": "音量ステップ設定", "polaritySetting": "極性設定", "outputLevel": "出力レベル", "lineMode": "ラインモード", "f1MinimumPhase": "F-1 最小位相", "f2LinearPhase": "F-2 線形位相", "f3BrickWall": "F-3 ブリックウォール", "dsdDirect": "DSDダイレクト", "volumeMemoryMode": "音量メモリーモード", "peqMemoryMode": "PEQメモリーモード", "mainButtonFunction": "メインボタン機能", "remoteAButtonFunction": "リモートAボタン機能", "remoteBButtonFunction": "リモートBボタン機能", "signal": "信号", "followOutput": "出力に従う", "globalMemory": "グローバルメモリー", "noMemory": "メモリーなし", "headphoneOutputSettingInDevelopment": "ヘッドフォン出力設定は開発中です", "volumeStepSelectionInDevelopment": "音量ステップ選択機能は開発中です", "polaritySelectionInDevelopment": "極性選択機能は開発中です", "outputLevelSelectionInDevelopment": "出力レベル選択機能は開発中です", "lineModeSelectionInDevelopment": "ラインモード選択機能は開発中です", "homePageSelectionInDevelopment": "ホームページ選択機能は開発中です", "mainButtonFunctionSelectionInDevelopment": "メインボタン機能選択は開発中です", "remoteAButtonFunctionSelectionInDevelopment": "リモートAボタン機能選択は開発中です", "remoteBButtonFunctionSelectionInDevelopment": "リモートBボタン機能選択は開発中です"}