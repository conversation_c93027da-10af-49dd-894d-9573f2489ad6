import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:topping_home/theme/color_palettes.dart';
import 'package:topping_home/theme/text_styles.dart';
import '../../../../common/util/i18n.dart';
import '../../../../common/widget/background_wrapper.dart';
import '../../../../models/d900_device_settings.dart';
import '../../../../models/dx5_device_settings.dart';
import '../../device_detail_logic.dart';
import '../../device_detail_state.dart';

/// 输入设置页面
class InputSettingsPage extends StatelessWidget {
  late final DeviceDetailLogic logic;
  late final DeviceDetailState state;
  late final String deviceId;
  late final bool isD900Device;

  dynamic get settings => state.settings.value;

  InputSettingsPage({super.key}) {
    final args = Get.arguments as Map<String, dynamic>;
    deviceId = args['deviceId'] ?? '';
    isD900Device = args['isD900Device'] ?? false;

    // 尝试获取已存在的 DeviceDetailLogic 实例
    try {
      logic = Get.find<DeviceDetailLogic>();
      state = logic.state;
    } catch (e) {
      // 如果没有找到，创建一个新的实例
      logic = Get.put(DeviceDetailLogic());
      state = logic.state;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundWrapper(
      child: Scaffold(
        backgroundColor: ColorPalettes.instance.transparent,
        appBar: AppBar(
          backgroundColor: ColorPalettes.instance.card,
          title: Text(
            l10n.inputSettings,
            style: TextStyles.instance.h2(),
          ),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: ColorPalettes.instance.firstText,
            ),
            onPressed: () => Get.back(),
          ),
          iconTheme: IconThemeData(color: ColorPalettes.instance.firstText),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildInputSelectCard(context),
                const SizedBox(height: 6),
                _buildUsbAudioClassCard(context),
                const SizedBox(height: 6),
                if (isD900Device) ...[
                  _buildUsbPortSelectCard(context),
                  const SizedBox(height: 6),
                  _buildIisPhaseCard(context),
                  const SizedBox(height: 6),
                  _buildIisDsdChannelCard(context),
                  const SizedBox(height: 6),
                  _buildDsdMuteCard(context),
                ] else ...[
                  _buildBluetoothCard(context),
                  const SizedBox(height: 6),
                  _buildBluetoothAptxCard(context),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建输入选择卡片
  Widget _buildInputSelectCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: l10n.inputSelect,
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              l10n.inputSelect,
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getInputText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showInputSelectionDialog(context),
          )),
    );
  }

  /// 构建USB音频类卡片
  Widget _buildUsbAudioClassCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "USB音频类",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "USB音频类",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getUsbAudioClassText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showUsbAudioClassSelectionDialog(context),
          )),
    );
  }

  /// 构建USB接口选择卡片 (D900专用)
  Widget _buildUsbPortSelectCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "USB接口选择",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "USB接口选择",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getUsbPortSelectText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showUsbPortSelectSelectionDialog(context),
          )),
    );
  }

  /// 构建IIS相位卡片 (D900专用)
  Widget _buildIisPhaseCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "IIS相位",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "IIS相位",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getIisPhaseText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showIisPhaseSelectionDialog(context),
          )),
    );
  }

  /// 构建IIS DSD通道卡片 (D900专用)
  Widget _buildIisDsdChannelCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "IIS DSD通道",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "IIS DSD通道",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getIisDsdChannelText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showIisDsdChannelSelectionDialog(context),
          )),
    );
  }

  /// 构建DSD MUTE卡片 (D900专用)
  Widget _buildDsdMuteCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "DSD MUTE",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "DSD MUTE",
              style: TextStyles.instance.h3(),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getDsdMuteText(),
                  style: TextStyles.instance.h3(),
                ),
                const SizedBox(width: 4),
                Icon(Icons.arrow_forward_ios,
                    color: ColorPalettes.instance.firstText, size: 12),
              ],
            ),
            onTap: () => _showDsdMuteSelectionDialog(context),
          )),
    );
  }

  /// 构建蓝牙功能卡片 (DX5专用)
  Widget _buildBluetoothCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "蓝牙功能",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "蓝牙功能",
              style: TextStyles.instance.h3(),
            ),
            trailing: Transform.scale(
              scale: 0.8,
              child: Switch(
                value: _getBluetoothEnabled(),
                activeColor: ColorPalettes.instance.accent,
                onChanged: (value) => _setBluetoothEnabled(value),
              ),
            ),
          )),
    );
  }

  /// 构建蓝牙aptX卡片 (DX5专用)
  Widget _buildBluetoothAptxCard(BuildContext context) {
    return _buildSettingCard(
      context,
      title: "蓝牙aptX",
      child: Obx(() => ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            title: Text(
              "蓝牙aptX",
              style: TextStyles.instance.h3(),
            ),
            trailing: Transform.scale(
              scale: 0.8,
              child: Switch(
                value: _getBluetoothAptxEnabled(),
                activeColor: ColorPalettes.instance.accent,
                onChanged: (value) => _setBluetoothAptxEnabled(value),
              ),
            ),
          )),
    );
  }

  /// 构建设置卡片
  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Card(
      color: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      margin: EdgeInsets.symmetric(vertical: 3),
      child: child,
    );
  }

  /// 获取输入源文本
  String _getInputText() {
    if (settings != null) {
      return settings.selectedInput?.localized(Get.context!) ?? "USB";
    }
    return "USB";
  }

  /// 获取USB音频类文本
  String _getUsbAudioClassText() {
    int usbClassValue = _getUsbAudioClassValue();
    return usbClassValue == 0 ? "UAC 1.0" : "UAC 2.0";
  }

  /// 获取USB音频类值
  int _getUsbAudioClassValue() {
    if (settings != null) {
      if (isD900Device && settings is D900DeviceSettings) {
        var d900Settings = settings as D900DeviceSettings;
        return d900Settings.usbType?.index ?? 1;
      } else if (!isD900Device && settings is Dx5DeviceSettings) {
        var dx5Settings = settings as Dx5DeviceSettings;
        return dx5Settings.usbType?.index ?? 1;
      }
    }
    return 1;
  }

  /// 获取USB接口选择文本 (D900)
  String _getUsbPortSelectText() {
    // TODO: 实现USB接口选择文本获取
    return "自动";
  }

  /// 获取IIS相位文本 (D900)
  String _getIisPhaseText() {
    // TODO: 实现IIS相位文本获取
    return "Standard";
  }

  /// 获取IIS DSD通道文本 (D900)
  String _getIisDsdChannelText() {
    // TODO: 实现IIS DSD通道文本获取
    return "Standard";
  }

  /// 获取DSD MUTE文本 (D900)
  String _getDsdMuteText() {
    // TODO: 实现DSD MUTE文本获取
    return "高电平有效";
  }

  /// 获取蓝牙功能状态 (DX5)
  bool _getBluetoothEnabled() {
    if (settings != null && !isD900Device && settings is Dx5DeviceSettings) {
      var dx5Settings = settings as Dx5DeviceSettings;
      return dx5Settings.audioBluetooth ?? false;
    }
    return false;
  }

  /// 设置蓝牙功能状态 (DX5)
  void _setBluetoothEnabled(bool value) {
    logic.toggleAudioBluetooth(value);
  }

  /// 获取蓝牙aptX状态 (DX5)
  bool _getBluetoothAptxEnabled() {
    if (settings != null && !isD900Device && settings is Dx5DeviceSettings) {
      var dx5Settings = settings as Dx5DeviceSettings;
      return dx5Settings.bluetoothAPTX ?? false;
    }
    return false;
  }

  /// 设置蓝牙aptX状态 (DX5)
  void _setBluetoothAptxEnabled(bool value) {
    logic.toggleBluetoothAptx(value);
  }

  /// 显示输入选择对话框
  void _showInputSelectionDialog(BuildContext context) {
    if (isD900Device) {
      _showD900InputSelectionDialog(context);
      return;
    }
    _showDx5InputSelectionDialog(context);
  }

  /// 显示DX5输入选择对话框
  void _showDx5InputSelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.inputSelect,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableInputs.length,
                  itemBuilder: (context, index) {
                    final input = state.availableInputs[index];
                    bool isSelected = settings?.selectedInput == input;
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            input.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setInput(input);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示D900输入选择对话框
  void _showD900InputSelectionDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.inputSelect,
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: state.availableInputs.length,
                  itemBuilder: (context, index) {
                    final input = state.availableInputs[index];
                    bool isSelected = false;

                    if (settings is D900DeviceSettings) {
                      var d900Settings = settings as D900DeviceSettings;
                      isSelected = d900Settings.selectedInput == input;
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            input.localized(context),
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setInput(input);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示USB音频类选择对话框
  void _showUsbAudioClassSelectionDialog(BuildContext context) {
    final usbClassList = [
      {'name': 'UAC 1.0', 'value': 0},
      {'name': 'UAC 2.0', 'value': 1},
    ];

    showModalBottomSheet(
      context: context,
      backgroundColor: ColorPalettes.instance.card,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    "USB音频类", // TODO: 添加到国际化文件
                    style: TextStyles.instance.h3(),
                  ),
                ),
                Divider(color: ColorPalettes.instance.divider),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: usbClassList.length,
                  itemBuilder: (context, index) {
                    final usbClass = usbClassList[index];
                    bool isSelected = _getUsbAudioClassValue() == usbClass['value'];
                    return ListTile(
                      title: Row(
                        children: [
                          Text(
                            usbClass['name'] as String,
                            style: TextStyles.instance.h3(),
                          ),
                          if (isSelected) ...[
                            SizedBox(width: 8),
                            Icon(
                              Icons.check_circle,
                              color: ColorPalettes.instance.accent,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        logic.setUsbMode(usbClass['value'] as int);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示USB接口选择对话框
  void _showUsbPortSelectSelectionDialog(BuildContext context) {
    // TODO: 实现USB接口选择对话框
    Get.snackbar("提示", "USB接口选择功能开发中");
  }

  /// 显示IIS相位选择对话框
  void _showIisPhaseSelectionDialog(BuildContext context) {
    // TODO: 实现IIS相位选择对话框
    Get.snackbar("提示", "IIS相位选择功能开发中");
  }

  /// 显示IIS DSD通道选择对话框
  void _showIisDsdChannelSelectionDialog(BuildContext context) {
    // TODO: 实现IIS DSD通道选择对话框
    Get.snackbar("提示", "IIS DSD通道选择功能开发中");
  }

  /// 显示DSD MUTE选择对话框
  void _showDsdMuteSelectionDialog(BuildContext context) {
    // TODO: 实现DSD MUTE选择对话框
    Get.snackbar("提示", "DSD MUTE选择功能开发中");
  }
}
